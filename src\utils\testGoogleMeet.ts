import { GoogleMeetService } from '@/lib/googleMeet';
import { googleCalendarService } from '@/lib/googleCalendar';

export interface GoogleMeetTestResult {
  success: boolean;
  message: string;
  details?: any;
  error?: string;
}

/**
 * Test Google Meet availability for a user
 */
export async function testGoogleMeetAvailability(userId: string): Promise<GoogleMeetTestResult> {
  try {
    console.log('🧪 Testing Google Meet availability for user:', userId);
    
    const isAvailable = await GoogleMeetService.isAvailable(userId);
    
    if (isAvailable) {
      return {
        success: true,
        message: '✅ Google Meet is available for this user',
        details: { userId, available: true }
      };
    } else {
      return {
        success: false,
        message: '❌ Google Meet is not available for this user',
        details: { userId, available: false },
        error: 'Google Calendar not connected or access token unavailable'
      };
    }
  } catch (error) {
    console.error('Error testing Google Meet availability:', error);
    return {
      success: false,
      message: '❌ Error testing Google Meet availability',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Test standalone Google Meet link generation
 */
export async function testStandaloneMeetLink(userId: string): Promise<GoogleMeetTestResult> {
  try {
    console.log('🧪 Testing standalone Google Meet link generation for user:', userId);
    
    const result = await GoogleMeetService.generateMeetLinkWithFallback(userId, 'Test Meeting');
    
    if (result.success && result.meetLink) {
      return {
        success: true,
        message: '✅ Standalone Google Meet link generated successfully',
        details: { 
          userId, 
          meetLink: result.meetLink,
          meetingId: result.meetLink.split('/').pop()
        }
      };
    } else {
      return {
        success: false,
        message: '❌ Failed to generate standalone Google Meet link',
        error: result.error || 'Unknown error'
      };
    }
  } catch (error) {
    console.error('Error testing standalone Meet link generation:', error);
    return {
      success: false,
      message: '❌ Error testing standalone Meet link generation',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Test individual meeting creation with Google Meet
 */
export async function testIndividualMeetingCreation(userId: string): Promise<GoogleMeetTestResult> {
  try {
    console.log('🧪 Testing individual meeting creation with Google Meet for user:', userId);
    
    const testStartTime = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now
    const testEndTime = new Date(testStartTime.getTime() + 60 * 60 * 1000); // 1 hour duration
    
    const result = await GoogleMeetService.createMeetingWithFallback(
      userId,
      {
        title: 'Test Individual Meeting',
        description: 'This is a test meeting created by the Google Meet integration test',
        startTime: testStartTime,
        endTime: testEndTime,
        attendees: [
          {
            email: '<EMAIL>',
            displayName: 'Test User',
          },
        ],
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      }
    );
    
    if (result.success && result.eventId && result.meetLink) {
      // Clean up: delete the test event
      try {
        await googleCalendarService.initializeForUser(userId);
        await googleCalendarService.deleteEvent(result.eventId);
        console.log('🧹 Test event cleaned up successfully');
      } catch (cleanupError) {
        console.warn('⚠️ Failed to clean up test event:', cleanupError);
      }
      
      return {
        success: true,
        message: '✅ Individual meeting with Google Meet created successfully',
        details: { 
          userId, 
          eventId: result.eventId,
          meetLink: result.meetLink,
          meetingId: result.meetLink.split('/').pop()
        }
      };
    } else {
      return {
        success: false,
        message: '❌ Failed to create individual meeting with Google Meet',
        error: result.error || 'Unknown error'
      };
    }
  } catch (error) {
    console.error('Error testing individual meeting creation:', error);
    return {
      success: false,
      message: '❌ Error testing individual meeting creation',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Test group meeting creation with Google Meet
 */
export async function testGroupMeetingCreation(userId: string): Promise<GoogleMeetTestResult> {
  try {
    console.log('🧪 Testing group meeting creation with Google Meet for user:', userId);
    
    const testStartTime = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now
    const testEndTime = new Date(testStartTime.getTime() + 60 * 60 * 1000); // 1 hour duration
    
    const result = await GoogleMeetService.createMeetingWithFallback(
      userId,
      {
        title: 'Test Group Meeting',
        description: 'This is a test group meeting created by the Google Meet integration test',
        startTime: testStartTime,
        endTime: testEndTime,
        attendees: [
          {
            email: '<EMAIL>',
            displayName: 'Test User 1',
          },
          {
            email: '<EMAIL>',
            displayName: 'Test User 2',
          },
        ],
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      }
    );
    
    if (result.success && result.eventId && result.meetLink) {
      // Clean up: delete the test event
      try {
        await googleCalendarService.initializeForUser(userId);
        await googleCalendarService.deleteEvent(result.eventId);
        console.log('🧹 Test event cleaned up successfully');
      } catch (cleanupError) {
        console.warn('⚠️ Failed to clean up test event:', cleanupError);
      }
      
      return {
        success: true,
        message: '✅ Group meeting with Google Meet created successfully',
        details: { 
          userId, 
          eventId: result.eventId,
          meetLink: result.meetLink,
          meetingId: result.meetLink.split('/').pop()
        }
      };
    } else {
      return {
        success: false,
        message: '❌ Failed to create group meeting with Google Meet',
        error: result.error || 'Unknown error'
      };
    }
  } catch (error) {
    console.error('Error testing group meeting creation:', error);
    return {
      success: false,
      message: '❌ Error testing group meeting creation',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Run comprehensive Google Meet integration tests
 */
export async function runGoogleMeetTests(userId: string): Promise<{
  overall: boolean;
  results: GoogleMeetTestResult[];
}> {
  console.log('🚀 Starting comprehensive Google Meet integration tests for user:', userId);
  
  const results: GoogleMeetTestResult[] = [];
  
  // Test 1: Check availability
  const availabilityTest = await testGoogleMeetAvailability(userId);
  results.push(availabilityTest);
  
  if (!availabilityTest.success) {
    console.log('⏭️ Skipping remaining tests due to availability failure');
    return {
      overall: false,
      results
    };
  }
  
  // Test 2: Standalone Meet link generation
  const standaloneLinkTest = await testStandaloneMeetLink(userId);
  results.push(standaloneLinkTest);
  
  // Test 3: Individual meeting creation
  const individualMeetingTest = await testIndividualMeetingCreation(userId);
  results.push(individualMeetingTest);
  
  // Test 4: Group meeting creation
  const groupMeetingTest = await testGroupMeetingCreation(userId);
  results.push(groupMeetingTest);
  
  const overall = results.every(result => result.success);
  
  console.log('📊 Google Meet integration test results:', {
    overall,
    passed: results.filter(r => r.success).length,
    failed: results.filter(r => !r.success).length,
    total: results.length
  });
  
  return {
    overall,
    results
  };
}
