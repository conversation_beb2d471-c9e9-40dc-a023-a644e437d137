import { googleCalendarService, CalendarEvent, GoogleMeetLink } from './googleCalendar';

export interface MeetingDetails {
  title: string;
  description?: string;
  startTime: Date;
  endTime: Date;
  attendees?: Array<{
    email: string;
    displayName?: string;
  }>;
  timezone?: string;
}

export interface CreateMeetingResult {
  eventId: string | null;
  meetLink: string | null;
  success: boolean;
  error?: string;
}

/**
 * Google Meet Service
 * Provides high-level methods for creating Google Meet links and managing meetings
 */
export class GoogleMeetService {
  /**
   * Create a meeting with Google Meet link
   * This creates a calendar event with an attached Google Meet link
   */
  static async createMeeting(
    userId: string,
    meetingDetails: MeetingDetails,
    calendarId: string = 'primary'
  ): Promise<CreateMeetingResult> {
    try {
      console.log('GoogleMeetService: Creating meeting with Meet link', { 
        title: meetingDetails.title,
        userId 
      });

      // Initialize the calendar service for the user
      await googleCalendarService.initializeForUser(userId);

      // Prepare the calendar event
      const calendarEvent: Omit<CalendarEvent, 'conferenceData'> = {
        summary: meetingDetails.title,
        description: meetingDetails.description,
        start: {
          dateTime: meetingDetails.startTime.toISOString(),
          timeZone: meetingDetails.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
        },
        end: {
          dateTime: meetingDetails.endTime.toISOString(),
          timeZone: meetingDetails.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
        },
        attendees: meetingDetails.attendees,
      };

      // Create the event with Google Meet
      const result = await googleCalendarService.createEventWithMeet(calendarEvent, calendarId);

      if (result.eventId && result.meetLink) {
        console.log('✅ Meeting created successfully with Meet link:', result.meetLink);
        return {
          eventId: result.eventId,
          meetLink: result.meetLink,
          success: true,
        };
      } else {
        console.error('❌ Failed to create meeting or generate Meet link');
        return {
          eventId: result.eventId,
          meetLink: result.meetLink,
          success: false,
          error: 'Failed to create meeting or generate Meet link',
        };
      }
    } catch (error) {
      console.error('Error creating meeting with Meet link:', error);
      return {
        eventId: null,
        meetLink: null,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Generate a standalone Google Meet link without creating a persistent calendar event
   * This is useful when you just need a Meet link for immediate use
   */
  static async generateMeetLink(
    userId: string,
    title: string = 'Meeting'
  ): Promise<{ meetLink: string | null; success: boolean; error?: string }> {
    try {
      console.log('GoogleMeetService: Generating standalone Meet link', { title, userId });

      // Initialize the calendar service for the user
      await googleCalendarService.initializeForUser(userId);

      // Generate the Meet link
      const meetLink = await googleCalendarService.generateMeetLink(title);

      if (meetLink) {
        console.log('✅ Meet link generated successfully:', meetLink);
        return {
          meetLink,
          success: true,
        };
      } else {
        console.error('❌ Failed to generate Meet link');
        return {
          meetLink: null,
          success: false,
          error: 'Failed to generate Meet link',
        };
      }
    } catch (error) {
      console.error('Error generating Meet link:', error);
      return {
        meetLink: null,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Generate a standalone Meet link with retry logic and better error handling
   */
  static async generateMeetLinkWithFallback(
    userId: string,
    title: string = 'Meeting'
  ): Promise<{ meetLink: string | null; success: boolean; error?: string }> {
    // First check if Google Meet is available
    const isAvailable = await this.isAvailable(userId);
    if (!isAvailable) {
      console.warn('Google Meet not available for user:', userId);
      return {
        meetLink: null,
        success: false,
        error: 'Google Calendar not connected or access token unavailable',
      };
    }

    // Try to generate Meet link with retries
    const maxRetries = 2;
    let lastError: string | undefined;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Attempt ${attempt}/${maxRetries} to generate Meet link`);
        const result = await this.generateMeetLink(userId, title);

        if (result.success) {
          return result;
        }

        lastError = result.error;
        console.warn(`Attempt ${attempt} failed:`, result.error);

        // Wait before retry
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      } catch (error) {
        lastError = error instanceof Error ? error.message : 'Unknown error';
        console.error(`Attempt ${attempt} threw error:`, error);

        // Wait before retry
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }

    return {
      meetLink: null,
      success: false,
      error: `Failed after ${maxRetries} attempts. Last error: ${lastError}`,
    };
  }

  /**
   * Create a meeting for individual booking
   */
  static async createIndividualMeeting(
    hostUserId: string,
    guestName: string,
    guestEmail: string,
    startTime: Date,
    endTime: Date,
    notes?: string,
    timezone?: string
  ): Promise<CreateMeetingResult> {
    const meetingDetails: MeetingDetails = {
      title: `Meeting with ${guestName}`,
      description: `Meeting booked through Schedulo.\n\nGuest: ${guestName}\nEmail: ${guestEmail}${notes ? `\nNotes: ${notes}` : ''}`,
      startTime,
      endTime,
      attendees: [
        {
          email: guestEmail,
          displayName: guestName,
        },
      ],
      timezone,
    };

    return this.createMeeting(hostUserId, meetingDetails);
  }

  /**
   * Create a meeting for group booking
   */
  static async createGroupMeeting(
    organizerUserId: string,
    title: string,
    description: string,
    startTime: Date,
    endTime: Date,
    attendeeEmails: string[],
    organizerName?: string,
    organizerEmail?: string,
    timezone?: string
  ): Promise<CreateMeetingResult> {
    const attendees = attendeeEmails.map(email => ({
      email,
      displayName: email.split('@')[0], // Use email prefix as display name
    }));

    // Add organizer to attendees if provided
    if (organizerEmail && !attendeeEmails.includes(organizerEmail)) {
      attendees.push({
        email: organizerEmail,
        displayName: organizerName || organizerEmail.split('@')[0],
      });
    }

    const meetingDetails: MeetingDetails = {
      title,
      description,
      startTime,
      endTime,
      attendees,
      timezone,
    };

    return this.createMeeting(organizerUserId, meetingDetails);
  }

  /**
   * Check if Google Meet integration is available for a user
   */
  static async isAvailable(userId: string): Promise<boolean> {
    try {
      await googleCalendarService.initializeForUser(userId);
      const token = await googleCalendarService.getAccessToken();
      return !!token;
    } catch (error) {
      console.error('Error checking Google Meet availability:', error);
      return false;
    }
  }

  /**
   * Create a meeting with comprehensive error handling and fallback options
   */
  static async createMeetingWithFallback(
    userId: string,
    meetingDetails: MeetingDetails,
    calendarId: string = 'primary'
  ): Promise<CreateMeetingResult> {
    // First check if Google Meet is available
    const isAvailable = await this.isAvailable(userId);
    if (!isAvailable) {
      console.warn('Google Meet not available for user:', userId);
      return {
        eventId: null,
        meetLink: null,
        success: false,
        error: 'Google Calendar not connected or access token unavailable',
      };
    }

    // Try to create meeting with retries
    const maxRetries = 2;
    let lastError: string | undefined;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Attempt ${attempt}/${maxRetries} to create Google Meet meeting`);
        const result = await this.createMeeting(userId, meetingDetails, calendarId);

        if (result.success) {
          return result;
        }

        lastError = result.error;
        console.warn(`Attempt ${attempt} failed:`, result.error);

        // Wait before retry (exponential backoff)
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      } catch (error) {
        lastError = error instanceof Error ? error.message : 'Unknown error';
        console.error(`Attempt ${attempt} threw error:`, error);

        // Wait before retry
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }

    return {
      eventId: null,
      meetLink: null,
      success: false,
      error: `Failed after ${maxRetries} attempts. Last error: ${lastError}`,
    };
  }

  /**
   * Extract Meet link from a calendar event
   */
  static async getMeetLinkFromEvent(
    userId: string,
    eventId: string,
    calendarId: string = 'primary'
  ): Promise<string | null> {
    try {
      await googleCalendarService.initializeForUser(userId);
      return await googleCalendarService.getMeetLinkFromEvent(eventId, calendarId);
    } catch (error) {
      console.error('Error getting Meet link from event:', error);
      return null;
    }
  }
}

// Export a singleton instance
export const googleMeetService = new GoogleMeetService();
