'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { 
  runGoogleMeetTests, 
  testGoogleMeetAvailability,
  testStandaloneMeetLink,
  testIndividualMeetingCreation,
  testGroupMeetingCreation,
  GoogleMeetTestResult 
} from '@/utils/testGoogleMeet';

export default function GoogleMeetTestPanel() {
  const { user, userProfile } = useAuth();
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<GoogleMeetTestResult[]>([]);
  const [overallResult, setOverallResult] = useState<boolean | null>(null);

  const runAllTests = async () => {
    if (!user?.uid) {
      alert('Please log in to run tests');
      return;
    }

    setTesting(true);
    setResults([]);
    setOverallResult(null);

    try {
      const testResults = await runGoogleMeetTests(user.uid);
      setResults(testResults.results);
      setOverallResult(testResults.overall);
    } catch (error) {
      console.error('Error running Google Meet tests:', error);
      setResults([{
        success: false,
        message: 'Failed to run tests',
        error: error instanceof Error ? error.message : 'Unknown error'
      }]);
      setOverallResult(false);
    } finally {
      setTesting(false);
    }
  };

  const runSingleTest = async (testType: string) => {
    if (!user?.uid) {
      alert('Please log in to run tests');
      return;
    }

    setTesting(true);

    try {
      let result: GoogleMeetTestResult;
      
      switch (testType) {
        case 'availability':
          result = await testGoogleMeetAvailability(user.uid);
          break;
        case 'standalone':
          result = await testStandaloneMeetLink(user.uid);
          break;
        case 'individual':
          result = await testIndividualMeetingCreation(user.uid);
          break;
        case 'group':
          result = await testGroupMeetingCreation(user.uid);
          break;
        default:
          throw new Error('Unknown test type');
      }

      setResults([result]);
      setOverallResult(result.success);
    } catch (error) {
      console.error('Error running single test:', error);
      setResults([{
        success: false,
        message: `Failed to run ${testType} test`,
        error: error instanceof Error ? error.message : 'Unknown error'
      }]);
      setOverallResult(false);
    } finally {
      setTesting(false);
    }
  };

  const getStatusIcon = (success: boolean) => {
    return success ? '✅' : '❌';
  };

  const getStatusColor = (success: boolean) => {
    return success ? 'text-green-600' : 'text-red-600';
  };

  if (!user) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Google Meet Integration Test</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">Please log in to test Google Meet integration.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Google Meet Integration Test</CardTitle>
        <p className="text-sm text-gray-600">
          Test the Google Meet integration functionality for user: {user.email}
        </p>
        <p className="text-sm text-gray-600">
          Google Calendar Connected: {userProfile?.googleCalendarConnected ? '✅ Yes' : '❌ No'}
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Test Buttons */}
        <div className="grid grid-cols-2 gap-4">
          <Button
            onClick={runAllTests}
            disabled={testing}
            className="w-full"
          >
            {testing ? <LoadingSpinner size="sm" /> : 'Run All Tests'}
          </Button>
          <Button
            onClick={() => runSingleTest('availability')}
            disabled={testing}
            variant="outline"
            className="w-full"
          >
            Test Availability
          </Button>
          <Button
            onClick={() => runSingleTest('standalone')}
            disabled={testing}
            variant="outline"
            className="w-full"
          >
            Test Standalone Link
          </Button>
          <Button
            onClick={() => runSingleTest('individual')}
            disabled={testing}
            variant="outline"
            className="w-full"
          >
            Test Individual Meeting
          </Button>
          <Button
            onClick={() => runSingleTest('group')}
            disabled={testing}
            variant="outline"
            className="w-full"
          >
            Test Group Meeting
          </Button>
        </div>

        {/* Overall Result */}
        {overallResult !== null && (
          <div className={`p-4 rounded-lg border ${overallResult ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
            <h3 className={`font-semibold ${getStatusColor(overallResult)}`}>
              {getStatusIcon(overallResult)} Overall Result: {overallResult ? 'PASSED' : 'FAILED'}
            </h3>
          </div>
        )}

        {/* Test Results */}
        {results.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-semibold text-gray-900">Test Results:</h3>
            {results.map((result, index) => (
              <div
                key={index}
                className={`p-3 rounded-lg border ${
                  result.success 
                    ? 'bg-green-50 border-green-200' 
                    : 'bg-red-50 border-red-200'
                }`}
              >
                <div className={`font-medium ${getStatusColor(result.success)}`}>
                  {getStatusIcon(result.success)} {result.message}
                </div>
                
                {result.error && (
                  <div className="mt-2 text-sm text-red-600">
                    <strong>Error:</strong> {result.error}
                  </div>
                )}
                
                {result.details && (
                  <div className="mt-2 text-sm text-gray-600">
                    <strong>Details:</strong>
                    <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                      {JSON.stringify(result.details, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Instructions */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-semibold text-blue-900 mb-2">Testing Instructions:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Ensure Google Calendar is connected in Settings</li>
            <li>• Run "Test Availability" first to check connection</li>
            <li>• Individual and Group tests create temporary calendar events</li>
            <li>• Test events are automatically cleaned up after testing</li>
            <li>• Check browser console for detailed logs</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
